import{r as m,T as E,x as H,o as l,c as r,a as n,u as M,w as c,F as g,Z as Y,b as e,k as h,y as Z,g as U,v as S,d as x,e as G,f as J,n as j,O as T,t as d,p as K,m as Q}from"./app-5886ce47.js";import{_ as R,a as W,b as X}from"./AdminLayout-2a8d334f.js";import{_ as ee}from"./CreateButton-3db2ab58.js";import{_ as te}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as se}from"./SecondaryButton-771950cd.js";import{D as oe}from"./DangerButton-afa878bd.js";import{M as le}from"./Modal-ba9b0639.js";import{_ as ae}from"./Pagination-10a81a9a.js";import{_ as v}from"./InputLabel-2616fd75.js";/* empty css                                                              */const o=a=>(K("data-v-a36e9b3f"),a=a(),Q(),a),ie={class:"animate-top"},re={class:"flex justify-between items-center"},ne=o(()=>e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Prospects")],-1)),de={class:"flex justify-end"},ce={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},ue={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},me=o(()=>e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),ge={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},_e={class:"flex justify-end"},pe={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},fe={class:"flex justify-between items-center mb-2"},he={class:"flex items-center"},xe=o(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),ve={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},ye=o(()=>e("option",{value:""},"All Statuses",-1)),be=["value"],we=o(()=>e("option",{value:""},"All Priorities",-1)),ke=["value"],Ce=o(()=>e("option",{value:""},"All Sources",-1)),Se=["value"],$e=o(()=>e("option",{value:""},"All Users",-1)),Ae=["value"],Me={class:"mt-8 overflow-x-auto sm:rounded-lg"},Ue={class:"shadow sm:rounded-lg"},je={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Be=o(()=>e("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[e("tr",{class:"border-b-2"},[e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," Prospect "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," Source "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," Status "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," Priority "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," Follow Up "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," ACTION ")])],-1)),Pe={key:0},De=["onClick"],Le={class:"flex items-center"},Ve={class:"text-sm font-medium text-gray-900"},Te={class:"text-sm text-gray-500"},ze=["onClick"],Ne={class:"text-sm text-gray-900"},Ie=["onClick"],Fe=["onClick"],Oe=["onClick"],qe={class:"items-center px-4 py-2.5"},Ee={class:"flex items-center justify-start gap-4"},He=o(()=>e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)),Ye=o(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),Ze=o(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),Ge=["onClick"],Je=o(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Ke=o(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)),Qe=[Je,Ke],Re={key:1},We=o(()=>e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),Xe=[We],et={class:"p-6"},tt=o(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Delete Prospect ",-1)),st=o(()=>e("p",{class:"mt-1 text-sm text-gray-600"}," Are you sure you want to delete this prospect? This action cannot be undone. ",-1)),ot={class:"mt-6 flex justify-end space-x-3"},lt={__name:"List",props:{prospects:Object,filters:Object,filterOptions:Object},setup(a){var B,P,D,L,V;const _=a,y=m(((B=_.filters)==null?void 0:B.search)||""),b=m(((P=_.filters)==null?void 0:P.status)||""),w=m(((D=_.filters)==null?void 0:D.priority)||""),k=m(((L=_.filters)==null?void 0:L.lead_source)||""),C=m(((V=_.filters)==null?void 0:V.assigned_to)||""),$=E({}),p=m(!1),A=m(null);H([y,b,w,k,C],()=>{T.get(route("prospects.index"),{search:y.value,status:b.value,priority:w.value,lead_source:k.value,assigned_to:C.value},{preserveState:!0,replace:!0})},{debounce:300});const z=i=>{A.value=i,p.value=!0},N=()=>{$.delete(route("prospects.destroy",A.value),{onSuccess:()=>{p.value=!1,A.value=null}})},I=i=>({new:"bg-blue-100 text-blue-800",contacted:"bg-yellow-100 text-yellow-800",qualified:"bg-green-100 text-green-800",unqualified:"bg-red-100 text-red-800",converted:"bg-purple-100 text-purple-800",lost:"bg-red-100 text-red-800"})[i]||"bg-gray-100 text-gray-800",F=i=>({low:"bg-gray-100 text-gray-800",medium:"bg-blue-100 text-blue-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[i]||"bg-gray-100 text-gray-800",O=i=>{if(!i)return"-";const s=new Date(i),t=String(s.getDate()).padStart(2,"0"),u=String(s.getMonth()+1).padStart(2,"0"),q=s.getFullYear();return`${t}-${u}-${q}`},f=i=>{T.visit(route("prospects.show",i))};return(i,s)=>(l(),r(g,null,[n(M(Y),{title:"Prospects"}),n(R,null,{default:c(()=>[e("div",ie,[e("div",re,[ne,e("div",de,[e("div",ce,[e("div",ue,[me,h(e("input",{id:"search-field","onUpdate:modelValue":s[0]||(s[0]=t=>y.value=t),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,512),[[Z,y.value]])])]),e("div",ge,[e("div",_e,[n(ee,{href:i.route("prospects.create")},{default:c(()=>[U(" Add New Prospect ")]),_:1},8,["href"])])])])]),e("div",pe,[e("div",fe,[e("div",he,[xe,n(v,{for:"customer_id",value:"Filters",class:"ml-2"})])]),e("div",ve,[e("div",null,[n(v,{for:"status",value:"Status"}),h(e("select",{id:"status","onUpdate:modelValue":s[1]||(s[1]=t=>b.value=t),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[ye,(l(!0),r(g,null,x(a.filterOptions.statuses,t=>(l(),r("option",{key:t,value:t},d(t.charAt(0).toUpperCase()+t.slice(1)),9,be))),128))],512),[[S,b.value]])]),e("div",null,[n(v,{for:"priority",value:"Priority"}),h(e("select",{id:"priority","onUpdate:modelValue":s[2]||(s[2]=t=>w.value=t),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[we,(l(!0),r(g,null,x(a.filterOptions.priorities,t=>(l(),r("option",{key:t,value:t},d(t.charAt(0).toUpperCase()+t.slice(1)),9,ke))),128))],512),[[S,w.value]])]),e("div",null,[n(v,{for:"lead_source",value:"Lead Source"}),h(e("select",{id:"lead_source","onUpdate:modelValue":s[3]||(s[3]=t=>k.value=t),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[Ce,(l(!0),r(g,null,x(a.filterOptions.leadSources,t=>(l(),r("option",{key:t,value:t},d(t.charAt(0).toUpperCase()+t.slice(1).replace("_"," ")),9,Se))),128))],512),[[S,k.value]])]),e("div",null,[n(v,{for:"assigned_to",value:"Assigned To"}),h(e("select",{id:"assigned_to","onUpdate:modelValue":s[4]||(s[4]=t=>C.value=t),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[$e,(l(!0),r(g,null,x(a.filterOptions.users,t=>(l(),r("option",{key:t.id,value:t.id},d(t.name),9,Ae))),128))],512),[[S,C.value]])])])]),e("div",Me,[e("div",Ue,[e("table",je,[Be,a.prospects.data.length>0?(l(),r("tbody",Pe,[(l(!0),r(g,null,x(a.prospects.data,t=>(l(),r("tr",{class:"odd:bg-white even:bg-gray-50 border-b cursor-pointer",key:t.id},[e("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36",onClick:u=>f(t.id)},[e("div",Le,[e("div",null,[e("div",Ve,d(t.first_name)+" "+d(t.last_name),1),e("div",Te,d(t.email),1)])])],8,De),e("td",{class:"px-4 py-2.5 min-w-36",onClick:u=>f(t.id)},[e("span",Ne,d(t.lead_source.charAt(0).toUpperCase()+t.lead_source.slice(1).replace("_"," ")),1)],8,ze),e("td",{class:"px-4 py-2.5 min-w-36",onClick:u=>f(t.id)},[e("span",{class:j([I(t.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},d(t.status.charAt(0).toUpperCase()+t.status.slice(1)),3)],8,Ie),e("td",{class:"px-4 py-2.5 min-w-36",onClick:u=>f(t.id)},[e("span",{class:j([F(t.priority),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},d(t.priority.charAt(0).toUpperCase()+t.priority.slice(1)),3)],8,Fe),e("td",{class:"px-4 py-2.5 min-w-36",onClick:u=>f(t.id)},d(O(t.next_follow_up_at)),9,Oe),e("td",qe,[e("div",Ee,[n(W,{align:"right",width:"48"},{trigger:c(()=>[He]),content:c(()=>[n(X,{href:i.route("prospects.edit",t.id)},{svg:c(()=>[Ye]),text:c(()=>[Ze]),_:2},1032,["href"]),e("button",{type:"button",onClick:u=>z(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Qe,8,Ge)]),_:2},1024)])])]))),128))])):(l(),r("tbody",Re,Xe))])])]),a.prospects.links.length>0?(l(),G(ae,{key:0,class:"mt-6",links:a.prospects.links},null,8,["links"])):J("",!0)]),n(le,{show:p.value,onClose:s[6]||(s[6]=t=>p.value=!1)},{default:c(()=>[e("div",et,[tt,st,e("div",ot,[n(se,{onClick:s[5]||(s[5]=t=>p.value=!1)},{default:c(()=>[U(" Cancel ")]),_:1}),n(oe,{onClick:N,class:j({"opacity-25":M($).processing}),disabled:M($).processing},{default:c(()=>[U(" Delete Prospect ")]),_:1},8,["class","disabled"])])])]),_:1},8,["show"])]),_:1})],64))}},pt=te(lt,[["__scopeId","data-v-a36e9b3f"]]);export{pt as default};

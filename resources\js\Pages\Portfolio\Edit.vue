<script setup>
import AdminLayout from "@/Layouts/AdminLayout.vue";
import { Head, Link, useForm } from '@inertiajs/vue3';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import SvgLink from '@/Components/ActionLink.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import CreateButton from '@/Components/CreateButton.vue';

const props = defineProps(['portfolio']);

const form = useForm({
    project_name: props.portfolio.project_name,
    url: props.portfolio.url || '',
    description: props.portfolio.description || '',
    technology: Array.isArray(props.portfolio.technology) ? props.portfolio.technology : [],
    login_id: props.portfolio.login_id || '',
    password: props.portfolio.password || '',
});

// Get technologies from config
const technologies = [
    // Frontend Technologies
    { id: 'html', name: 'HTML' },
    { id: 'css', name: 'CSS' },
    { id: 'javascript', name: 'JavaScript' },
    { id: 'typescript', name: 'TypeScript' },
    { id: 'react', name: 'React' },
    { id: 'vue', name: 'Vue.js' },
    { id: 'angular', name: 'Angular' },
    { id: 'svelte', name: 'Svelte' },
    { id: 'jquery', name: 'jQuery' },
    { id: 'bootstrap', name: 'Bootstrap' },
    { id: 'tailwind', name: 'Tailwind CSS' },
    { id: 'sass', name: 'Sass/SCSS' },

    // Backend Technologies
    { id: 'php', name: 'PHP' },
    { id: 'laravel', name: 'Laravel' },
    { id: 'codeigniter', name: 'CodeIgniter' },
    { id: 'symfony', name: 'Symfony' },
    { id: 'nodejs', name: 'Node.js' },
    { id: 'express', name: 'Express.js' },
    { id: 'python', name: 'Python' },
    { id: 'django', name: 'Django' },
    { id: 'flask', name: 'Flask' },
    { id: 'java', name: 'Java' },
    { id: 'spring', name: 'Spring Boot' },
    { id: 'csharp', name: 'C#' },
    { id: 'dotnet', name: '.NET' },
    { id: 'ruby', name: 'Ruby' },
    { id: 'rails', name: 'Ruby on Rails' },
    { id: 'go', name: 'Go' },
    { id: 'rust', name: 'Rust' },

    // Databases
    { id: 'mysql', name: 'MySQL' },
    { id: 'postgresql', name: 'PostgreSQL' },
    { id: 'mongodb', name: 'MongoDB' },
    { id: 'sqlite', name: 'SQLite' },
    { id: 'redis', name: 'Redis' },
    { id: 'elasticsearch', name: 'Elasticsearch' },

    // Cloud & DevOps
    { id: 'aws', name: 'AWS' },
    { id: 'azure', name: 'Microsoft Azure' },
    { id: 'gcp', name: 'Google Cloud Platform' },
    { id: 'docker', name: 'Docker' },
    { id: 'kubernetes', name: 'Kubernetes' },
    { id: 'jenkins', name: 'Jenkins' },
    { id: 'gitlab', name: 'GitLab CI/CD' },
    { id: 'github-actions', name: 'GitHub Actions' },

    // Mobile Development
    { id: 'react-native', name: 'React Native' },
    { id: 'flutter', name: 'Flutter' },
    { id: 'ionic', name: 'Ionic' },
    { id: 'xamarin', name: 'Xamarin' },

    // Other Tools & Technologies
    { id: 'git', name: 'Git' },
    { id: 'webpack', name: 'Webpack' },
    { id: 'vite', name: 'Vite' },
    { id: 'nginx', name: 'Nginx' },
    { id: 'apache', name: 'Apache' },
    { id: 'linux', name: 'Linux' },
    { id: 'ubuntu', name: 'Ubuntu' },
    { id: 'centos', name: 'CentOS' },
];

const submit = () => {
    form.patch(route('portfolios.update', props.portfolio.id));
};
</script>

<template>
    <Head title="Portfolio" />

    <AdminLayout>
        <div class="items-start">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Edit Portfolio Project</h1>
                <div class="flex space-x-2">
                    <CreateButton :href="route('portfolios.show', portfolio.id)">
                        View Project
                    </CreateButton>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <form @submit.prevent="submit" class="space-y-6">
                    <!-- Project Name -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <InputLabel for="project_name" value="Project Name *" />
                        <TextInput
                            id="project_name"
                            v-model="form.project_name"
                            type="text"
                            class="mt-1 block w-full"
                            required
                            autofocus
                            placeholder="Enter project name"
                        />
                        <InputError class="mt-2" :message="form.errors.project_name" />
                    </div>

                    <!-- URL -->
                    <div>
                        <InputLabel for="url" value="Project URL" />
                        <TextInput
                            id="url"
                            v-model="form.url"
                            type="url"
                            class="mt-1 block w-full"
                            placeholder="https://example.com"
                        />
                        <InputError class="mt-2" :message="form.errors.url" />
                    </div>

                    <!-- Description -->
                    <div>
                        <InputLabel for="description" value="Description" />
                        <textarea
                            id="description"
                            v-model="form.description"
                            rows="3"
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            placeholder="Describe your project..."
                        ></textarea>
                        <InputError class="mt-2" :message="form.errors.description" />
                    </div>

                    <!-- Technology -->
                    <div>
                        <InputLabel for="technology" value="Technologies Used" />
                        <div class="mt-2 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-60 overflow-y-auto border border-gray-300 rounded-md p-4">
                            <div v-for="tech in technologies" :key="tech.id" class="flex items-center">
                                <input
                                    :id="`tech-${tech.id}`"
                                    v-model="form.technology"
                                    :value="tech.id"
                                    type="checkbox"
                                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                />
                                <label :for="`tech-${tech.id}`" class="ml-2 text-sm text-gray-700">
                                    {{ tech.name }}
                                </label>
                            </div>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Select all technologies used in this project</p>
                        <InputError class="mt-2" :message="form.errors.technology" />
                    </div>

                    <!-- Login Credentials -->
                    <div>
                        <InputLabel for="login_id" value="Login ID" />
                        <TextInput
                            id="login_id"
                            v-model="form.login_id"
                            type="text"
                            class="mt-1 block w-full"
                            placeholder="Enter login ID (optional)"
                        />
                        <InputError class="mt-2" :message="form.errors.login_id" />
                    </div>

                    <div>
                        <InputLabel for="password" value="Password" />
                        <TextInput
                            id="password"
                            v-model="form.password"
                            type="text"
                            class="mt-1 block w-full"
                            placeholder="Enter password (optional)"
                        />
                        <InputError class="mt-2" :message="form.errors.password" />
                    </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex mt-6 items-center justify-between">
                        <div class="ml-auto flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('portfolios.index')">
                                <template #svg>
                                    <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Back</button>
                                </template>
                        </SvgLink>
                        <PrimaryButton :disabled="form.processing">Update</PrimaryButton>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </AdminLayout>
</template>

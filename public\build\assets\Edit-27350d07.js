import{T as x,o as p,c as g,a as o,u as t,w as d,F as f,Z as w,b as e,g as _,h as j,k as y,y as S,d as k,G as V,t as P}from"./app-5886ce47.js";import{_ as C,b as D}from"./AdminLayout-2a8d334f.js";import{_ as n}from"./InputLabel-2616fd75.js";import{_ as m}from"./TextInput-4566519e.js";import{_ as r}from"./InputError-ff051a07.js";import{P as L}from"./PrimaryButton-2c7b1831.js";import{_ as U}from"./CreateButton-3db2ab58.js";import"./_plugin-vue_export-helper-c27b6911.js";const E={class:"items-start"},B={class:"flex justify-between items-center mb-6"},N=e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Portfolio Project",-1),R={class:"flex space-x-2"},T={class:"bg-white rounded-lg shadow p-6"},$=["onSubmit"],A={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},M={class:"mt-2 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-60 overflow-y-auto border border-gray-300 rounded-md p-4"},G=["id","value"],q=["for"],I=e("p",{class:"mt-1 text-sm text-gray-500"},"Select all technologies used in this project",-1),F={class:"flex mt-6 items-center justify-between"},Q={class:"ml-auto flex items-center justify-end gap-x-6"},H=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Back",-1),ee={__name:"Edit",props:["portfolio"],setup(c){const l=c,s=x({project_name:l.portfolio.project_name,url:l.portfolio.url||"",description:l.portfolio.description||"",technology:Array.isArray(l.portfolio.technology)?l.portfolio.technology:[],login_id:l.portfolio.login_id||"",password:l.portfolio.password||""}),h=[{id:"html",name:"HTML"},{id:"css",name:"CSS"},{id:"javascript",name:"JavaScript"},{id:"typescript",name:"TypeScript"},{id:"react",name:"React"},{id:"vue",name:"Vue.js"},{id:"angular",name:"Angular"},{id:"svelte",name:"Svelte"},{id:"jquery",name:"jQuery"},{id:"bootstrap",name:"Bootstrap"},{id:"tailwind",name:"Tailwind CSS"},{id:"sass",name:"Sass/SCSS"},{id:"php",name:"PHP"},{id:"laravel",name:"Laravel"},{id:"codeigniter",name:"CodeIgniter"},{id:"symfony",name:"Symfony"},{id:"nodejs",name:"Node.js"},{id:"express",name:"Express.js"},{id:"python",name:"Python"},{id:"django",name:"Django"},{id:"flask",name:"Flask"},{id:"java",name:"Java"},{id:"spring",name:"Spring Boot"},{id:"csharp",name:"C#"},{id:"dotnet",name:".NET"},{id:"ruby",name:"Ruby"},{id:"rails",name:"Ruby on Rails"},{id:"go",name:"Go"},{id:"rust",name:"Rust"},{id:"mysql",name:"MySQL"},{id:"postgresql",name:"PostgreSQL"},{id:"mongodb",name:"MongoDB"},{id:"sqlite",name:"SQLite"},{id:"redis",name:"Redis"},{id:"elasticsearch",name:"Elasticsearch"},{id:"aws",name:"AWS"},{id:"azure",name:"Microsoft Azure"},{id:"gcp",name:"Google Cloud Platform"},{id:"docker",name:"Docker"},{id:"kubernetes",name:"Kubernetes"},{id:"jenkins",name:"Jenkins"},{id:"gitlab",name:"GitLab CI/CD"},{id:"github-actions",name:"GitHub Actions"},{id:"react-native",name:"React Native"},{id:"flutter",name:"Flutter"},{id:"ionic",name:"Ionic"},{id:"xamarin",name:"Xamarin"},{id:"git",name:"Git"},{id:"webpack",name:"Webpack"},{id:"vite",name:"Vite"},{id:"nginx",name:"Nginx"},{id:"apache",name:"Apache"},{id:"linux",name:"Linux"},{id:"ubuntu",name:"Ubuntu"},{id:"centos",name:"CentOS"}],b=()=>{s.patch(route("portfolios.update",l.portfolio.id))};return(u,a)=>(p(),g(f,null,[o(t(w),{title:"Portfolio"}),o(C,null,{default:d(()=>[e("div",E,[e("div",B,[N,e("div",R,[o(U,{href:u.route("portfolios.show",c.portfolio.id)},{default:d(()=>[_(" View Project ")]),_:1},8,["href"])])]),e("div",T,[e("form",{onSubmit:j(b,["prevent"]),class:"space-y-6"},[e("div",A,[e("div",null,[o(n,{for:"project_name",value:"Project Name *"}),o(m,{id:"project_name",modelValue:t(s).project_name,"onUpdate:modelValue":a[0]||(a[0]=i=>t(s).project_name=i),type:"text",class:"mt-1 block w-full",required:"",autofocus:"",placeholder:"Enter project name"},null,8,["modelValue"]),o(r,{class:"mt-2",message:t(s).errors.project_name},null,8,["message"])]),e("div",null,[o(n,{for:"url",value:"Project URL"}),o(m,{id:"url",modelValue:t(s).url,"onUpdate:modelValue":a[1]||(a[1]=i=>t(s).url=i),type:"url",class:"mt-1 block w-full",placeholder:"https://example.com"},null,8,["modelValue"]),o(r,{class:"mt-2",message:t(s).errors.url},null,8,["message"])]),e("div",null,[o(n,{for:"description",value:"Description"}),y(e("textarea",{id:"description","onUpdate:modelValue":a[2]||(a[2]=i=>t(s).description=i),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Describe your project..."},null,512),[[S,t(s).description]]),o(r,{class:"mt-2",message:t(s).errors.description},null,8,["message"])]),e("div",null,[o(n,{for:"technology",value:"Technologies Used"}),e("div",M,[(p(),g(f,null,k(h,i=>e("div",{key:i.id,class:"flex items-center"},[y(e("input",{id:`tech-${i.id}`,"onUpdate:modelValue":a[3]||(a[3]=v=>t(s).technology=v),value:i.id,type:"checkbox",class:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"},null,8,G),[[V,t(s).technology]]),e("label",{for:`tech-${i.id}`,class:"ml-2 text-sm text-gray-700"},P(i.name),9,q)])),64))]),I,o(r,{class:"mt-2",message:t(s).errors.technology},null,8,["message"])]),e("div",null,[o(n,{for:"login_id",value:"Login ID"}),o(m,{id:"login_id",modelValue:t(s).login_id,"onUpdate:modelValue":a[4]||(a[4]=i=>t(s).login_id=i),type:"text",class:"mt-1 block w-full",placeholder:"Enter login ID (optional)"},null,8,["modelValue"]),o(r,{class:"mt-2",message:t(s).errors.login_id},null,8,["message"])]),e("div",null,[o(n,{for:"password",value:"Password"}),o(m,{id:"password",modelValue:t(s).password,"onUpdate:modelValue":a[5]||(a[5]=i=>t(s).password=i),type:"text",class:"mt-1 block w-full",placeholder:"Enter password (optional)"},null,8,["modelValue"]),o(r,{class:"mt-2",message:t(s).errors.password},null,8,["message"])])]),e("div",F,[e("div",Q,[o(D,{href:u.route("portfolios.index")},{svg:d(()=>[H]),_:1},8,["href"]),o(L,{disabled:t(s).processing},{default:d(()=>[_("Update")]),_:1},8,["disabled"])])])],40,$)])])]),_:1})],64))}};export{ee as default};

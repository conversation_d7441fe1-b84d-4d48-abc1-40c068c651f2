import{r as V,T as k,o as i,c as d,a as l,u as n,w as m,F as S,Z as Q,b as t,t as a,f as c,d as A,g as p,n as D,h as $,k as x,v as M,y as F,G as Y}from"./app-5886ce47.js";import{_ as Z,b as j}from"./AdminLayout-2a8d334f.js";import{P as C}from"./PrimaryButton-2c7b1831.js";import{_ as g}from"./SecondaryButton-771950cd.js";import{M as N}from"./Modal-ba9b0639.js";import{_ as f}from"./InputLabel-2616fd75.js";import{_ as U}from"./TextInput-4566519e.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const H={class:"animate-top"},J={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},K={class:"text-3xl font-bold text-gray-900"},X=t("p",{class:"text-gray-600 mt-1"},"Prospect Details",-1),tt={class:"flex space-x-4"},et=t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Back",-1),st={class:"grid grid-cols-1 lg:grid-cols-9 gap-6"},ot={class:"lg:col-span-6 space-y-6"},lt={class:"bg-white shadow rounded-lg p-6"},at=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Basic Information",-1),it={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},dt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Client Name",-1),nt={class:"mt-1 text-sm text-gray-700"},ct=t("label",{class:"block text-sm font-semibold text-gray-900"},"Email",-1),rt={class:"mt-1 text-sm text-gray-700"},ut={key:0},mt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Phone",-1),pt={class:"mt-1 text-sm text-gray-700"},ft={key:1},_t=t("label",{class:"block text-sm font-semibold text-gray-900"},"Company",-1),bt={class:"mt-1 text-sm text-gray-700"},xt={key:2},gt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Position",-1),yt={class:"mt-1 text-sm text-gray-700"},ht={key:3},vt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Location",-1),wt={class:"mt-1 text-sm text-gray-700"},kt={class:"bg-white shadow rounded-lg p-6"},St=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Activity Timeline",-1),Ct={key:0,class:"space-y-4"},Ut={class:"flex-shrink-0"},Vt={class:"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center"},At={class:"text-indigo-600 text-xs font-medium"},$t={class:"flex-1 min-w-0"},Ft={class:"flex items-center justify-between"},Nt={class:"text-sm font-semibold text-gray-900"},Pt={key:0,class:"mt-1 text-sm text-gray-500"},Dt={class:"flex items-center justify-between"},Mt={key:0,class:"mt-1 text-xs text-gray-600"},jt={class:"text-xs text-gray-600"},qt={key:1,class:"text-center py-8 text-gray-500"},Bt={class:"lg:col-span-3 space-y-6"},Lt={class:"bg-white shadow rounded-lg p-6"},Tt=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quick Actions",-1),It={class:"flex flex-wrap gap-3"},Et={key:0,class:"bg-white shadow rounded-lg p-6"},Ot=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Notes & Conversation",-1),Rt={class:"space-y-4"},Wt={key:0},zt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Initial Conversation:",-1),Gt={class:"mt-1 text-sm text-gray-700"},Qt={key:1},Yt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Requirements:",-1),Zt={class:"mt-1 text-sm text-gray-700"},Ht={key:2},Jt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Notes:",-1),Kt={class:"mt-1 text-sm text-gray-700"},Xt={key:1,class:"bg-white shadow rounded-lg p-6"},te=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"URLs",-1),ee={class:"flex flex-col gap-1"},se=["href"],oe=["href"],le=["href"],ae={class:"bg-white shadow rounded-lg p-6"},ie=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Status & Priority",-1),de={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ne={class:""},ce=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status:",-1),re={class:""},ue=t("label",{class:"block text-sm font-semibold text-gray-900"},"Priority:",-1),me=t("label",{class:"block text-sm font-semibold text-gray-900"},"Score:",-1),pe={class:"mt-1 text-sm text-gray-700"},fe=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lead Source:",-1),_e={class:"mt-1 text-sm text-gray-700"},be={key:0},xe=t("label",{class:"block text-sm font-semibold text-gray-900"},"Assigned To:",-1),ge={class:"mt-1 text-sm text-gray-700"},ye={class:"bg-white shadow rounded-lg p-6"},he=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Project Information",-1),ve={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},we={key:0},ke=t("label",{class:"block text-sm font-semibold text-gray-900"},"Project Type:",-1),Se={class:"mt-1 text-sm text-gray-700"},Ce={key:1},Ue=t("label",{class:"block text-sm font-semibold text-gray-900"},"Estimated Budget:",-1),Ve={class:"mt-1 text-sm text-gray-700"},Ae={key:2},$e=t("label",{class:"block text-sm font-semibold text-gray-900"},"Next Follow-up:",-1),Fe={class:"mt-1 text-sm text-gray-700"},Ne={key:3},Pe=t("label",{class:"block text-sm font-semibold text-gray-900"},"Converted Lead:",-1),De={class:"p-6"},Me=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Add Activity",-1),je=["onSubmit"],qe=["value"],Be={class:"border-t pt-4"},Le={class:"flex items-center space-x-2"},Te={key:0,class:"mt-3"},Ie={class:"flex justify-end space-x-3"},Ee={class:"p-6"},Oe=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Update Status",-1),Re=["onSubmit"],We=["value"],ze={class:"flex justify-end space-x-3"},Ge={class:"p-6"},Qe=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Schedule Follow-up",-1),Ye=["onSubmit"],Ze={class:"flex justify-end space-x-3"},as={__name:"Show",props:{prospect:Object},setup(e){const w=e,y=V(!1),h=V(!1),v=V(!1),r=k({activity_type:"note_added",title:"",description:"",activity_date:new Date().toISOString().slice(0,10),schedule_followup:!1,followup_date:""}),_=k({status:w.prospect.status,notes:""}),b=k({next_follow_up_at:"",notes:""});k({});const q=()=>{r.post(route("prospects.addActivity",w.prospect.id),{onSuccess:()=>{y.value=!1,r.reset(),r.schedule_followup=!1,r.followup_date=""}})},B=()=>{_.patch(route("prospects.updateStatus",w.prospect.id),{onSuccess:()=>{h.value=!1,_.reset()}})},L=()=>{b.post(route("prospects.scheduleFollowUp",w.prospect.id),{onSuccess:()=>{v.value=!1,b.reset()}})},T=u=>({new:"bg-blue-100 text-blue-800",contacted:"bg-yellow-100 text-yellow-800",qualified:"bg-green-100 text-green-800",unqualified:"bg-red-100 text-red-800",converted:"bg-purple-100 text-purple-800",lost:"bg-red-100 text-red-800"})[u]||"bg-gray-100 text-gray-800",I=u=>({low:"bg-gray-100 text-gray-800",medium:"bg-blue-100 text-blue-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[u]||"bg-gray-100 text-gray-800",P=u=>{if(!u)return"-";const o=new Date(u),s=String(o.getDate()).padStart(2,"0"),z=String(o.getMonth()+1).padStart(2,"0"),G=o.getFullYear();return`${s}-${z}-${G}`},E=u=>u?new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(u):"-",O=u=>u.charAt(0).toUpperCase()+u.slice(1).replace("_"," "),R=[{value:"email_sent",label:"Email Sent"},{value:"email_received",label:"Email Received"},{value:"call_made",label:"Call Made"},{value:"call_received",label:"Call Received"},{value:"meeting_scheduled",label:"Meeting Scheduled"},{value:"meeting_completed",label:"Meeting Completed"},{value:"note_added",label:"Note Added"},{value:"linkedin_message",label:"LinkedIn Message"},{value:"proposal_sent",label:"Proposal Sent"},{value:"follow_up_scheduled",label:"Follow-up Scheduled"},{value:"document_shared",label:"Document Shared"},{value:"other",label:"Other"}],W=["new","contacted","qualified","unqualified","converted","lost"];return(u,o)=>(i(),d(S,null,[l(n(Q),{title:"Prospects"}),l(Z,null,{default:m(()=>[t("div",H,[t("div",J,[t("div",null,[t("h1",K,"Prospect: "+a(e.prospect.first_name)+" "+a(e.prospect.last_name),1),X]),t("div",tt,[l(j,{href:u.route("prospects.index")},{svg:m(()=>[et]),_:1},8,["href"])])]),t("div",st,[t("div",ot,[t("div",lt,[at,t("div",it,[t("div",null,[dt,t("p",nt,a(e.prospect.first_name)+" "+a(e.prospect.last_name),1)]),t("div",null,[ct,t("p",rt,a(e.prospect.email||"N/A"),1)]),e.prospect.phone?(i(),d("div",ut,[mt,t("p",pt,a(e.prospect.phone),1)])):c("",!0),e.prospect.company?(i(),d("div",ft,[_t,t("p",bt,a(e.prospect.company),1)])):c("",!0),e.prospect.position?(i(),d("div",xt,[gt,t("p",yt,a(e.prospect.position??"N/A"),1)])):c("",!0),e.prospect.country||e.prospect.city?(i(),d("div",ht,[vt,t("p",wt,a(e.prospect.city)+a(e.prospect.city&&e.prospect.country?", ":"")+a(e.prospect.country),1)])):c("",!0)])]),t("div",kt,[St,e.prospect.activities&&e.prospect.activities.length>0?(i(),d("div",Ct,[(i(!0),d(S,null,A(e.prospect.activities,s=>(i(),d("div",{key:s.id,class:"flex items-start space-x-3 p-4 bg-gray-50 rounded-lg"},[t("div",Ut,[t("div",Vt,[t("span",At,a(s.activity_type.charAt(0).toUpperCase()),1)])]),t("div",$t,[t("div",Ft,[t("p",Nt,a(s.title),1)]),s.description?(i(),d("p",Pt,a(s.description),1)):c("",!0),t("div",Dt,[s.user?(i(),d("p",Mt,"by "+a(s.user.first_name),1)):c("",!0),t("p",jt,a(P(s.activity_date)),1)])])]))),128))])):(i(),d("div",qt," No activities recorded yet. "))])]),t("div",Bt,[t("div",Lt,[Tt,t("div",It,[l(C,{onClick:o[0]||(o[0]=s=>y.value=!0)},{default:m(()=>[p(" Add Activity ")]),_:1}),l(g,{class:"w-full",onClick:o[1]||(o[1]=s=>h.value=!0)},{default:m(()=>[p(" Update Status ")]),_:1}),l(g,{class:"w-full",onClick:o[2]||(o[2]=s=>v.value=!0)},{default:m(()=>[p(" Schedule Follow-up ")]),_:1}),l(g,{class:"w-full",onClick:o[3]||(o[3]=s=>u.$inertia.visit(u.route("prospects.edit",e.prospect.id)))},{default:m(()=>[p(" Edit Prospect ")]),_:1})])]),e.prospect.initial_conversation||e.prospect.notes||e.prospect.requirements?(i(),d("div",Et,[Ot,t("div",Rt,[e.prospect.initial_conversation?(i(),d("div",Wt,[zt,t("p",Gt,a(e.prospect.initial_conversation),1)])):c("",!0),e.prospect.requirements?(i(),d("div",Qt,[Yt,t("p",Zt,a(e.prospect.requirements),1)])):c("",!0),e.prospect.notes?(i(),d("div",Ht,[Jt,t("p",Kt,a(e.prospect.notes),1)])):c("",!0)])])):c("",!0),e.prospect.linkedin_url||e.prospect.website_url||e.prospect.company_website?(i(),d("div",Xt,[te,t("div",ee,[e.prospect.linkedin_url?(i(),d("a",{key:0,href:e.prospect.linkedin_url,target:"_blank",class:"text-blue-600 font-semibold hover:text-blue-800"}," LinkedIn Profile ",8,se)):c("",!0),e.prospect.website_url?(i(),d("a",{key:1,href:e.prospect.website_url,target:"_blank",class:"text-blue-600 font-semibold hover:text-blue-800"}," Personal Website ",8,oe)):c("",!0),e.prospect.company_website?(i(),d("a",{key:2,href:e.prospect.company_website,target:"_blank",class:"text-blue-600 font-semibold hover:text-blue-800"}," Company Website ",8,le)):c("",!0)])])):c("",!0),t("div",ae,[ie,t("div",de,[t("div",ne,[ce,t("span",{class:D([T(e.prospect.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(e.prospect.status.charAt(0).toUpperCase()+e.prospect.status.slice(1)),3)]),t("div",re,[ue,t("span",{class:D([I(e.prospect.priority),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(e.prospect.priority.charAt(0).toUpperCase()+e.prospect.priority.slice(1)),3)]),t("div",null,[me,t("span",pe,a(e.prospect.score)+"/100",1)]),t("div",null,[fe,t("span",_e,a(O(e.prospect.lead_source)),1)]),e.prospect.assigned_user?(i(),d("div",be,[xe,t("span",ge,a(e.prospect.assigned_user.name),1)])):c("",!0)])]),t("div",ye,[he,t("div",ve,[e.prospect.project_type?(i(),d("div",we,[ke,t("p",Se,a(e.prospect.project_type),1)])):c("",!0),e.prospect.estimated_budget?(i(),d("div",Ce,[Ue,t("p",Ve,a(E(e.prospect.estimated_budget)),1)])):c("",!0),e.prospect.next_follow_up_at?(i(),d("div",Ae,[$e,t("p",Fe,a(P(e.prospect.next_follow_up_at)),1)])):c("",!0),e.prospect.converted_lead?(i(),d("div",Ne,[Pe,l(j,{href:u.route("leads.show",e.prospect.converted_lead.id),class:"ml-2 text-sm text-indigo-600"},{default:m(()=>[p(" View Lead #"+a(e.prospect.converted_lead.id),1)]),_:1},8,["href"])])):c("",!0)])])])])]),l(N,{show:y.value,onClose:o[11]||(o[11]=s=>y.value=!1)},{default:m(()=>[t("div",De,[Me,t("form",{onSubmit:$(q,["prevent"]),class:"space-y-4"},[t("div",null,[l(f,{for:"activity_type",value:"Activity Type"}),x(t("select",{id:"activity_type","onUpdate:modelValue":o[4]||(o[4]=s=>n(r).activity_type=s),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[(i(),d(S,null,A(R,s=>t("option",{key:s.value,value:s.value},a(s.label),9,qe)),64))],512),[[M,n(r).activity_type]])]),t("div",null,[l(f,{for:"activity_title",value:"Title"}),l(U,{id:"activity_title",modelValue:n(r).title,"onUpdate:modelValue":o[5]||(o[5]=s=>n(r).title=s),type:"text",class:"mt-1 block w-full",required:""},null,8,["modelValue"])]),t("div",null,[l(f,{for:"activity_description",value:"Description"}),x(t("textarea",{id:"activity_description","onUpdate:modelValue":o[6]||(o[6]=s=>n(r).description=s),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},null,512),[[F,n(r).description]])]),t("div",null,[l(f,{for:"activity_date",value:"Activity Date"}),l(U,{id:"activity_date",modelValue:n(r).activity_date,"onUpdate:modelValue":o[7]||(o[7]=s=>n(r).activity_date=s),type:"date",class:"mt-1 block w-full"},null,8,["modelValue"])]),t("div",Be,[t("div",Le,[x(t("input",{id:"schedule_followup","onUpdate:modelValue":o[8]||(o[8]=s=>n(r).schedule_followup=s),type:"checkbox",class:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"},null,512),[[Y,n(r).schedule_followup]]),l(f,{for:"schedule_followup",value:"Schedule Follow-up",class:"text-sm font-medium text-gray-700"})]),n(r).schedule_followup?(i(),d("div",Te,[l(f,{for:"followup_date",value:"Follow-up Date"}),l(U,{id:"followup_date",modelValue:n(r).followup_date,"onUpdate:modelValue":o[9]||(o[9]=s=>n(r).followup_date=s),type:"date",class:"mt-1 block w-full",min:new Date().toISOString().slice(0,10)},null,8,["modelValue","min"])])):c("",!0)]),t("div",Ie,[l(g,{onClick:o[10]||(o[10]=s=>y.value=!1)},{default:m(()=>[p("Cancel")]),_:1}),l(C,{disabled:n(r).processing},{default:m(()=>[p("Add Activity")]),_:1},8,["disabled"])])],40,je)])]),_:1},8,["show"]),l(N,{show:h.value,onClose:o[15]||(o[15]=s=>h.value=!1)},{default:m(()=>[t("div",Ee,[Oe,t("form",{onSubmit:$(B,["prevent"]),class:"space-y-4"},[t("div",null,[l(f,{for:"status",value:"Status"}),x(t("select",{id:"status","onUpdate:modelValue":o[12]||(o[12]=s=>n(_).status=s),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[(i(),d(S,null,A(W,s=>t("option",{key:s,value:s},a(s.charAt(0).toUpperCase()+s.slice(1)),9,We)),64))],512),[[M,n(_).status]])]),t("div",null,[l(f,{for:"status_notes",value:"Notes"}),x(t("textarea",{id:"status_notes","onUpdate:modelValue":o[13]||(o[13]=s=>n(_).notes=s),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Optional notes about the status change"},null,512),[[F,n(_).notes]])]),t("div",ze,[l(g,{onClick:o[14]||(o[14]=s=>h.value=!1)},{default:m(()=>[p("Cancel")]),_:1}),l(C,{disabled:n(_).processing},{default:m(()=>[p("Update Status")]),_:1},8,["disabled"])])],40,Re)])]),_:1},8,["show"]),l(N,{show:v.value,onClose:o[19]||(o[19]=s=>v.value=!1)},{default:m(()=>[t("div",Ge,[Qe,t("form",{onSubmit:$(L,["prevent"]),class:"space-y-4"},[t("div",null,[l(f,{for:"follow_up_date",value:"Follow-up Date"}),l(U,{id:"follow_up_date",modelValue:n(b).next_follow_up_at,"onUpdate:modelValue":o[16]||(o[16]=s=>n(b).next_follow_up_at=s),type:"date",class:"mt-1 block w-full",required:""},null,8,["modelValue"])]),t("div",null,[l(f,{for:"follow_up_notes",value:"Notes"}),x(t("textarea",{id:"follow_up_notes","onUpdate:modelValue":o[17]||(o[17]=s=>n(b).notes=s),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Notes about the follow-up"},null,512),[[F,n(b).notes]])]),t("div",Ze,[l(g,{onClick:o[18]||(o[18]=s=>v.value=!1)},{default:m(()=>[p("Cancel")]),_:1}),l(C,{disabled:n(b).processing},{default:m(()=>[p("Schedule Follow-up")]),_:1},8,["disabled"])])],40,Ye)])]),_:1},8,["show"])]),_:1})],64))}};export{as as default};

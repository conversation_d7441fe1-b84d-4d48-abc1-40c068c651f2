import{i as _,C as S,q as $,r as g,o as n,c as a,b as t,s as f,k as C,D as L,a as o,w as s,n as u,A as N,e as A,u as B,j,t as k,f as p,J as x,K as b,T as q,g as l}from"./app-5886ce47.js";const T={class:""},z={__name:"Dropdown",props:{align:{type:String,default:"right"},width:{type:String,default:"48"},contentClasses:{type:String,default:"py-1 bg-white"}},setup(i){const r=i,e=y=>{h.value&&y.key==="Escape"&&(h.value=!1)};_(()=>document.addEventListener("keydown",e)),S(()=>document.removeEventListener("keydown",e));const c=$(()=>({48:"w-48"})[r.width.toString()]),v=$(()=>r.align==="left"?"origin-top-left left-0":r.align==="right"?"origin-top-right right-0":"origin-top"),h=g(!1);return(y,m)=>(n(),a("div",T,[t("div",{onClick:m[0]||(m[0]=M=>h.value=!h.value)},[f(y.$slots,"trigger")]),C(t("div",{class:"fixed inset-0 z-40",onClick:m[1]||(m[1]=M=>h.value=!1)},null,512),[[L,h.value]]),o(N,{"enter-active-class":"transition ease-out duration-200","enter-from-class":"opacity-0 scale-95","enter-to-class":"opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"opacity-100 scale-100","leave-to-class":"opacity-0 scale-95"},{default:s(()=>[C(t("div",{class:u(["absolute z-50 mt-2 rounded-md shadow-lg",[c.value,v.value]]),style:{display:"none"},onClick:m[2]||(m[2]=M=>h.value=!1)},[t("div",{class:u(["rounded-md ring-1 ring-black ring-opacity-5",i.contentClasses])},[f(y.$slots,"content")],2)],2),[[L,h.value]])]),_:3})]))}};const d={__name:"SideMenu",props:{href:{type:String,required:!0},active:{type:Boolean}},setup(i){const r=i,e=$(()=>r.active?"block bg-gray-700 text-white group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold transition duration-150 ease-in-out":"block text-gray-500 hover:text-white hover:bg-gray-700 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold transition duration-150 ease-in-out");return(c,v)=>(n(),A(B(j),{href:i.href,class:u(e.value)},{default:s(()=>[f(c.$slots,"svg"),f(c.$slots,"name")]),_:3},8,["href","class"]))}},V={key:0,id:"toast-success","aria-live":"assertive",class:"pointer-events-none fixed inset-0 flex items-end px-4 py-6 sm:items-start sm:p-6 z-40"},E={class:"flex w-full flex-col items-center space-y-4 sm:items-end"},H={class:"pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-blue-100 border border-blue-400 shadow-lg ring-1 ring-black ring-opacity-5"},O={class:"p-4"},P={class:"flex items-start"},D=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-6 w-6 text-blue-700",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("circle",{cx:"12",cy:"12",r:"9","stroke-linecap":"round","stroke-linejoin":"round"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 8v0.01M12 11v5"})])],-1),F={class:"ml-3 w-0 flex-1 pt-0.5"},U={class:"text-sm font-medium text-blue-700"},G=x('<div class="ml-4 flex flex-shrink-0"><button type="button" class="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"><span class="sr-only">Close</span><svg class="h-5 w-5 text-blue-700 bg-blue-100" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"></path></svg></button></div>',1),W={__name:"ToastNotification",props:{message:String},setup(i){const r=g(!1),e=()=>{r.value=!1,b().props.flash.message=""};return _(()=>{r.value=!0,setTimeout(()=>e(),2e3)}),(c,v)=>r.value&&i.message?(n(),a("div",V,[t("div",E,[t("div",H,[t("div",O,[t("div",P,[D,t("div",F,[t("p",U,k(i.message)+"!",1)]),G])])])])])):p("",!0)}},J={key:0,id:"toast-success","aria-live":"assertive",class:"pointer-events-none fixed inset-0 flex items-end px-4 py-6 sm:items-start sm:p-6 z-40"},K={class:"flex w-full flex-col items-center space-y-4 sm:items-end"},Y={class:"pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-green-100 border border-green-400 shadow-lg ring-1 ring-black ring-opacity-5"},I={class:"p-4"},Q={class:"flex items-start"},R=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-6 w-6 text-green-700",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1),X={class:"ml-3 w-0 flex-1 pt-0.5"},Z={class:"text-sm font-medium text-green-700"},ee=x('<div class="ml-4 flex flex-shrink-0"><button type="button" class="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"><span class="sr-only">Close</span><svg class="h-5 w-5 text-green-700 bg-green-100" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"></path></svg></button></div>',1),te={__name:"ToastNotificationSuccess",props:{message:String},setup(i){const r=g(!1),e=()=>{r.value=!1,b().props.flash.success=""};return _(()=>{r.value=!0,setTimeout(()=>e(),2e3)}),(c,v)=>r.value&&i.message?(n(),a("div",J,[t("div",K,[t("div",Y,[t("div",I,[t("div",Q,[R,t("div",X,[t("p",Z,k(i.message)+"!",1)]),ee])])])])])):p("",!0)}},se={key:0,id:"toast-success","aria-live":"assertive",class:"pointer-events-none fixed inset-0 flex items-end px-4 py-6 sm:items-start sm:p-6 z-40"},re={class:"flex w-full flex-col items-center space-y-4 sm:items-end"},oe={class:"pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-red-100 border border-red-400 shadow-lg ring-1 ring-black ring-opacity-5"},ne={class:"p-4"},ie={class:"flex items-start"},ae=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("circle",{cx:"12",cy:"12",r:"9","stroke-linecap":"round","stroke-linejoin":"round"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8 8l8 8m0-8L8 16"})])],-1),le={class:"ml-3 w-0 flex-1 pt-0.5"},de={class:"text-sm font-medium text-red-700"},ue=x('<div class="ml-4 flex flex-shrink-0"><button type="button" class="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"><span class="sr-only">Close</span><svg class="h-5 w-5 text-red-700 bg-red-100" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"></path></svg></button></div>',1),ce={__name:"ToastNotificationError",props:{message:String},setup(i){const r=g(!1),e=()=>{r.value=!1,b().props.flash.error=""};return _(()=>{r.value=!0,setTimeout(()=>e(),2e3)}),(c,v)=>r.value&&i.message?(n(),a("div",se,[t("div",re,[t("div",oe,[t("div",ne,[t("div",ie,[ae,t("div",le,[t("p",de,k(i.message)+"!",1)]),ue])])])])])):p("",!0)}},he={key:0,id:"toast-success","aria-live":"assertive",class:"pointer-events-none fixed inset-0 flex items-end px-4 py-6 sm:items-start sm:p-6 z-40"},pe={class:"flex w-full flex-col items-center space-y-4 sm:items-end"},ge={class:"pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-yellow-100 border border-yellow-400 shadow-lg ring-1 ring-black ring-opacity-5"},fe={class:"p-4"},ve={class:"flex items-start"},me=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-6 w-6 text-yellow-700",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 2L2 22h20L12 2zM12 16v-4M12 20v-2"})])],-1),we={class:"ml-3 w-0 flex-1 pt-0.5"},_e={class:"text-sm font-medium text-yellow-700"},xe=x('<div class="ml-4 flex flex-shrink-0"><button type="button" class="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"><span class="sr-only">Close</span><svg class="h-5 w-5 text-yellow-700 bg-yellow-100" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"></path></svg></button></div>',1),ke={__name:"ToastNotificationWarning",props:{message:String},setup(i){const r=g(!1),e=()=>{r.value=!1,b().props.flash.warning=""};return _(()=>{r.value=!0,setTimeout(()=>e(),2e3)}),(c,v)=>r.value&&i.message?(n(),a("div",he,[t("div",pe,[t("div",ge,[t("div",fe,[t("div",ve,[me,t("div",we,[t("p",_e,k(i.message)+"!",1)]),xe])])])])])):p("",!0)}};const w={__name:"ActionLink",props:{href:{type:String,required:!0}},setup(i){return(r,e)=>(n(),A(B(j),{href:i.href,class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none transition duration-150 ease-in-out w-full"},{default:s(()=>[f(r.$slots,"svg"),f(r.$slots,"text")]),_:3},8,["href"]))}};const ye={class:"lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col border-t border-r bg-gray-900"},be={class:"bg-gray-900 flex h-16 px-6 items-center px-10 shrink-0 w-full mt-2"},$e=["src"],Me={class:"flex grow flex-col gap-y-2 overflow-y-auto bg-gray-900 mt-2"},Ce={class:"flex flex-1 flex-col px-6"},Le={role:"list",class:"flex flex-1 flex-col gap-y-7"},ze={role:"list",class:"-mx-2 space-y-1"},Ae=t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"},null,-1),Be=[Ae],je=t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"},null,-1),Se=[je],Ne=t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},null,-1),qe=[Ne],Te=t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"},null,-1),Ve=[Te],Ee=t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"},null,-1),He=[Ee],Oe=t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 9A3.75 3.75 0 1 1 12 5.25 3.75 3.75 0 0 1 15.75 9zM20.25 15.75a3 3 0 1 0-6 0m6 0A3.75 3.75 0 0 1 12 18m8.25-2.25v1.5A5.25 5.25 0 0 1 15 21m-3-15.75A3.75 3.75 0 1 1 5.25 9M9 15.75a3 3 0 1 0-6 0m6 0A3.75 3.75 0 0 1 3.75 18m5.25-2.25v1.5A5.25 5.25 0 0 1 3 21"},null,-1),Pe=[Oe],De=t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 12H8m8-4H8m8 8H8M5 5v14l-2-2m2 2 2-2M16 5l-4 4-4-4m8 0H8m8 0-4 4-4-4m0 8h8m-8-4h8m-8 4h8"},null,-1),Fe=[De],Ue=t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16.5 3h-9A2.5 2.5 0 005 5.5v13A2.5 2.5 0 007.5 21h9a2.5 2.5 0 002.5-2.5v-13A2.5 2.5 0 0016.5 3zM5 8l7 5 7-5"},null,-1),Ge=[Ue],We=t("svg",{class:"h-6 w-6 shrink-0"},[t("path",{d:"M2 10V4a2 2 0 0 1 2-2h6l10 10a2 2 0 0 1 0 3l-6 6a2 2 0 0 1-3 0L2 12V10z",stroke:"currentColor","stroke-width":"2",fill:"none"}),t("circle",{cx:"7",cy:"7",r:"2",fill:"currentColor"}),t("path",{d:"M15 12l 5M18 9l4",stroke:"currentColor","stroke-width":"2",fill:"none"})],-1),Je=t("svg",{class:"h-6 w-6 shrink-0",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[t("path",{d:"M6 18V6L12 12L18 6V18"})],-1),Ke=t("svg",{class:"h-6 w-6 shrink-0",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[t("path",{d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})],-1),Ye={class:"mt-auto px-4 mt-1"},Ie=t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z"},null,-1),Qe=t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),Re=[Ie,Qe],Xe={class:"lg:pl-72 border-t"},Ze={class:"sticky top-0 z-20 flex h-16 shrink-0 items-center gap-x-4 bg-white px-4 shadow sm:gap-x-6 sm:px-6 lg:px-8"},et=x('<button type="button" class="-m-2.5 p-2.5 text-gray-700 lg:hidden"><span class="sr-only">Open sidebar</span><svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path></svg></button><div class="h-6 w-px bg-gray-200 lg:hidden" aria-hidden="true"></div>',2),tt={class:"flex flex-1 gap-x-4 self-stretch lg:gap-x-6 justify-between"},st={class:"flex items-center"},rt=t("div",{class:"flex w-32"},[t("a",{class:"flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600",href:"#"}," Add New +")],-1),ot=t("span",{class:"text-sm text-gray-700 leading-6"},"Add New User",-1),nt=t("span",{class:"text-sm text-gray-700 leading-6"},"Add New Prospect",-1),it=t("span",{class:"text-sm text-gray-700 leading-6"},"Add New Portfolio",-1),at=t("span",{class:"text-sm text-gray-700 leading-6"},"Add New Template",-1),lt={class:"flex items-center gap-x-4 lg:gap-x-6"},dt=x('<button type="button" class="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500"><span class="sr-only">View Notifications</span><svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"></path></svg></button><div class="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200" aria-hidden="true"></div>',2),ut={class:"relative"},ct={type:"button",class:"-m-1.5 flex items-center p-1.5",id:"user-menu-button","aria-expanded":"false","aria-haspopup":"true"},ht=t("span",{class:"sr-only"},"Open User Menu",-1),pt=t("img",{class:"h-8 w-8 rounded-full bg-gray-50",src:"https://img.freepik.com/premium-photo/avatar-resourcing-company_1254967-6696.jpg?size=626&ext=jpg&ga=GA1.1.*********.1729255085&semt=ais_hybrid",alt:""},null,-1),gt={class:"hidden lg:flex lg:items-center"},ft={class:"ml-4 text-sm font-semibold leading-6 text-gray-900","aria-hidden":"true"},vt=t("svg",{class:"ml-2 h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z","clip-rule":"evenodd"})],-1),mt=t("span",{class:"text-sm text-gray-700 leading-5"},"Your Profile",-1),wt=t("span",{class:"text-sm text-gray-700 leading-5"},"Sign Out",-1),_t={key:0},xt={key:1},kt={key:2},yt={key:3},bt={class:"py-10 bg-gray-100"},$t={class:"px-4 sm:px-6 lg:px-8 min-h-screen"},Ct={__name:"AdminLayout",setup(i){g("/uploads/companyprofile/defaultimg.png"),q({});const r=g("/mototive.png");return _(async()=>{}),(e,c)=>(n(),a("div",null,[t("div",ye,[t("div",be,[t("img",{src:r.value,alt:"LOGO"},null,8,$e)]),t("div",Me,[t("nav",Ce,[t("ul",Le,[t("li",null,[t("ul",ze,[t("li",null,[o(d,{href:e.route("dashboard"),active:e.route().current("dashboard")},{svg:s(()=>[(n(),a("svg",{class:u(["h-6 w-6 shrink-0",e.route().current("dashboard")?"text-white":"text-gray-500 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},Be,2))]),name:s(()=>[l("Dashboard")]),_:1},8,["href","active"])]),t("li",null,[o(d,{href:e.route("users.index"),active:e.route().current("users.index")||e.route().current("users.create")||e.route().current("users.edit")},{svg:s(()=>[(n(),a("svg",{class:u(["h-6 w-6 shrink-0",e.route().current("users.index")||e.route().current("users.create")||e.route().current("users.edit")?"text-white":"text-gray-500 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},Se,2))]),name:s(()=>[l("Users")]),_:1},8,["href","active"])]),t("li",null,[o(d,{href:e.route("prospects.index"),active:e.route().current("prospects.index")||e.route().current("prospects.create")||e.route().current("prospects.edit")||e.route().current("prospects.show")},{svg:s(()=>[(n(),a("svg",{class:u(["h-6 w-6 shrink-0",e.route().current("prospects.index")||e.route().current("prospects.create")||e.route().current("prospects.edit")||e.route().current("prospects.show")?"text-white":"text-gray-500 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},qe,2))]),name:s(()=>[l("Prospects")]),_:1},8,["href","active"])]),t("li",null,[o(d,{href:e.route("portfolios.index"),active:e.route().current("portfolios.index")||e.route().current("portfolios.create")||e.route().current("portfolios.edit")||e.route().current("portfolios.show")},{svg:s(()=>[(n(),a("svg",{class:u(["h-6 w-6 shrink-0",e.route().current("portfolios.index")||e.route().current("portfolios.create")||e.route().current("portfolios.edit")||e.route().current("portfolios.show")?"text-white":"text-gray-500 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},Ve,2))]),name:s(()=>[l("Portfolio")]),_:1},8,["href","active"])]),t("li",null,[o(d,{href:e.route("templates.index"),active:e.route().current("templates.index")||e.route().current("templates.create")||e.route().current("templates.edit")||e.route().current("templates.show")},{svg:s(()=>[(n(),a("svg",{class:u(["h-6 w-6 shrink-0",e.route().current("templates.index")||e.route().current("templates.create")||e.route().current("templates.edit")||e.route().current("templates.show")?"text-white":"text-gray-500 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},He,2))]),name:s(()=>[l("Templates")]),_:1},8,["href","active"])]),t("li",null,[o(d,{href:e.route("leads.index"),active:e.route().current("leads.index")||e.route().current("leads.create")||e.route().current("leads.edit")},{svg:s(()=>[(n(),a("svg",{class:u(["h-6 w-6 shrink-0",e.route().current("leads.index")||e.route().current("leads.create")||e.route().current("leads.edit")?"text-white":"text-gray-500 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},Pe,2))]),name:s(()=>[l("Leads")]),_:1},8,["href","active"])]),t("li",null,[o(d,{href:e.route("email-sequence.index"),active:e.route().current("email-sequence.index")||e.route().current("email-sequence.create")||e.route().current("email-sequence.edit")||e.route().current("sequence-step.show")||e.route().current("sequence-step.create")||e.route().current("sequence-step.edit")},{svg:s(()=>[(n(),a("svg",{class:u(["h-6 w-6 shrink-0",e.route().current("email-sequence.index")||e.route().current("email-sequence.create")||e.route().current("email-sequence.edit")||e.route().current("sequence-step.show")||e.route().current("sequence-step.create")||e.route().current("sequence-step.edit")?"text-white":"text-gray-500 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},Fe,2))]),name:s(()=>[l("Sequences")]),_:1},8,["href","active"])]),t("li",null,[o(d,{href:e.route("smtp.index"),active:e.route().current("smtp.index")||e.route().current("smtp.create")||e.route().current("smtp.edit")},{svg:s(()=>[(n(),a("svg",{class:u(["h-6 w-6 shrink-0",e.route().current("smtp.index")||e.route().current("smtp.create")||e.route().current("smtp.edit")?"text-white":"text-gray-500 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},Ge,2))]),name:s(()=>[l("SMTP")]),_:1},8,["href","active"])]),t("li",null,[o(d,{href:e.route("email-tag.index"),active:e.route().current("email-tag.index")||e.route().current("email-tag.create")||e.route().current("email-tag.edit")},{svg:s(()=>[We]),name:s(()=>[l("Email Tags")]),_:1},8,["href","active"])]),t("li",null,[o(d,{href:e.route("sent-email.index"),active:e.route().current("sent-email.index")||e.route().current("sent-email.edit")},{svg:s(()=>[Je]),name:s(()=>[l("Sent Emails")]),_:1},8,["href","active"])]),t("li",null,[o(d,{href:e.route("failed-emails.index"),active:e.route().current("failed-emails.index")||e.route().current("failed-emails.edit")},{svg:s(()=>[Ke]),name:s(()=>[l("Failed Emails")]),_:1},8,["href","active"])])])])])])]),t("div",Ye,[o(d,{href:e.route("setting"),active:e.route().current("setting")||e.route().current("roles.permission")||e.route().current("roles.index")||e.route().current("roles.create")||e.route().current("roles.edit")},{svg:s(()=>[(n(),a("svg",{class:u(["h-6 w-6 shrink-0 text-gray-500",e.route().current("setting")||e.route().current("roles.index")||e.route().current("roles.create")||e.route().current("roles.edit")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},Re,2))]),name:s(()=>[l("Settings")]),_:1},8,["href","active"])])]),t("div",Xe,[t("div",Ze,[et,t("div",tt,[t("div",st,[o(z,{align:"left",width:"48"},{trigger:s(()=>[rt]),content:s(()=>[o(w,{href:e.route("users.create")},{svg:s(()=>[]),text:s(()=>[ot]),_:1},8,["href"]),o(w,{href:e.route("prospects.create")},{svg:s(()=>[]),text:s(()=>[nt]),_:1},8,["href"]),o(w,{href:e.route("portfolios.create")},{svg:s(()=>[]),text:s(()=>[it]),_:1},8,["href"]),o(w,{href:e.route("templates.create")},{svg:s(()=>[]),text:s(()=>[at]),_:1},8,["href"])]),_:1})]),t("div",lt,[dt,t("div",ut,[o(z,{align:"right",width:"48"},{trigger:s(()=>[t("button",ct,[ht,pt,t("span",gt,[t("span",ft,k(e.$page.props.auth.user.name),1),vt])])]),content:s(()=>[o(w,{href:e.route("profile.edit"),as:"button"},{svg:s(()=>[]),text:s(()=>[mt]),_:1},8,["href"]),o(w,{href:e.route("logout"),method:"post",as:"button"},{svg:s(()=>[]),text:s(()=>[wt]),_:1},8,["href"])]),_:1})])])])]),e.$page.props.flash.message?(n(),a("div",_t,[o(W,{message:e.$page.props.flash.message},null,8,["message"])])):p("",!0),e.$page.props.flash.success?(n(),a("div",xt,[o(te,{message:e.$page.props.flash.success},null,8,["message"])])):p("",!0),e.$page.props.flash.error?(n(),a("div",kt,[o(ce,{message:e.$page.props.flash.error},null,8,["message"])])):p("",!0),e.$page.props.flash.warning?(n(),a("div",yt,[o(ke,{message:e.$page.props.flash.warning},null,8,["message"])])):p("",!0),t("main",bt,[t("div",$t,[f(e.$slots,"default")])])])]))}};export{Ct as _,z as a,w as b};

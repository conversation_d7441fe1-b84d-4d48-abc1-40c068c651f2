import{_ as o}from"./AdminLayout-2a8d334f.js";import i from"./DeleteUserForm-c4766ceb.js";import m from"./UpdatePasswordForm-4d4c9c78.js";import r from"./UpdateProfileInformationForm-51bee660.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-5886ce47.js";import"./DangerButton-afa878bd.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-ff051a07.js";import"./InputLabel-2616fd75.js";import"./Modal-ba9b0639.js";/* empty css                                                              */import"./SecondaryButton-771950cd.js";import"./TextInput-4566519e.js";import"./PrimaryButton-2c7b1831.js";import"./TextArea-e741a213.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};

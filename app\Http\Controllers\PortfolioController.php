<?php

namespace App\Http\Controllers;

use App\Models\Portfolio;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Validation\Rule;

class PortfolioController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Portfolio::query();

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        $portfolios = $query->orderBy('created_at', 'desc')->paginate(15)->withQueryString();

        return Inertia::render('Portfolio/Index', [
            'portfolios' => $portfolios,
            'filters' => $request->only(['search']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Portfolio/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'project_name' => 'required|string|max:255',
            'url' => 'nullable|url|max:255',
            'description' => 'nullable|string|max:1000',
            'technology' => 'nullable|string|max:500',
            'login_id' => 'nullable|string|max:255',
            'password' => 'nullable|string|max:255',
        ]);

        try {
            Portfolio::create($validated);

            return Redirect::route('portfolios.index')
                ->with('success', 'Portfolio item created successfully.');
        } catch (\Exception $e) {
            return Redirect::back()
                ->with('error', 'Failed to create portfolio item: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Portfolio $portfolio)
    {
        return Inertia::render('Portfolio/Show', [
            'portfolio' => $portfolio,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Portfolio $portfolio)
    {
        return Inertia::render('Portfolio/Edit', [
            'portfolio' => $portfolio,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Portfolio $portfolio)
    {
        $validated = $request->validate([
            'project_name' => 'required|string|max:255',
            'url' => 'nullable|url|max:255',
            'description' => 'nullable|string|max:1000',
            'technology' => 'nullable|string|max:500',
            'login_id' => 'nullable|string|max:255',
            'password' => 'nullable|string|max:255',
        ]);

        try {
            $portfolio->update($validated);

            return Redirect::route('portfolios.index')
                ->with('success', 'Portfolio item updated successfully.');
        } catch (\Exception $e) {
            return Redirect::back()
                ->with('error', 'Failed to update portfolio item: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Portfolio $portfolio)
    {
        try {
            $portfolio->delete();

            return Redirect::route('portfolios.index')
                ->with('success', 'Portfolio item deleted successfully.');
        } catch (\Exception $e) {
            return Redirect::back()
                ->with('error', 'Failed to delete portfolio item: ' . $e->getMessage());
        }
    }
}

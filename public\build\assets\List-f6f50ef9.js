import{o as u,c as p,F as I,d as O,k as R,E as xe,b as e,t as x,r as f,q as S,e as ee,w as h,a,g as w,G as te,T as ye,i as we,x as E,u as be,Z as ke,h as Ce,f as q,O as Se,n as D,p as Le,m as Ae}from"./app-5886ce47.js";import{_ as Me,a as $e,b as qe}from"./AdminLayout-2a8d334f.js";import{_ as z}from"./CreateButton-3db2ab58.js";import{P as se}from"./PrimaryButton-2c7b1831.js";import{_ as P}from"./SecondaryButton-771950cd.js";import{D as Ve}from"./DangerButton-afa878bd.js";import{M as T}from"./Modal-ba9b0639.js";import{_ as Ie}from"./Pagination-10a81a9a.js";import{_ as N}from"./SearchableDropdownNew-ca0b65f1.js";import{_ as V}from"./InputLabel-2616fd75.js";import{_ as je}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const Ne={class:"flex space-x-4"},Oe=["value","id","onChange"],Be=["for"],Ue={__name:"RadioButton",props:{modelValue:{type:[String,Number,Boolean],required:!0},options:{type:Array,required:!0}},emits:["update:modelValue"],setup(n,{emit:i}){const r=n,v=_=>{i("update:modelValue",_)};return(_,d)=>(u(),p("div",Ne,[(u(!0),p(I,null,O(n.options,c=>(u(),p("div",{class:"flex items-center",key:c.value},[R(e("input",{type:"radio",value:c.value,id:c.value,"onUpdate:modelValue":d[0]||(d[0]=k=>r.modelValue=k),onChange:k=>v(c.value),class:"border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"},null,40,Oe),[[xe,r.modelValue]]),e("label",{for:c.value,class:"ml-2 cursor-pointer"},x(c.label),9,Be)]))),128))]))}},Ee={class:"p-6"},ze=e("h2",{class:"text-lg font-medium text-gray-900 mb-4"}," Select Lead ",-1),Pe={class:"mb-6"},Te={class:"mt-6"},Re={class:"flex justify-end space-x-4"},Fe={class:"w-24"},Je={class:"w-28"},Ge={__name:"SelectionModal",props:{show:{type:Boolean,default:!1},currentPageCount:{type:Number,required:!0},totalCount:{type:Number,required:!0}},emits:["close","apply","cancel"],setup(n,{emit:i}){const r=n,v=f("current"),_=S(()=>[{value:"current",label:`Current page (${r.currentPageCount} Leads)`},{value:"all",label:`All (${r.totalCount} Leads)`}]),d=()=>{i("close")},c=()=>{i("apply",v.value),d()},k=()=>{i("cancel"),d()};return(j,L)=>(u(),ee(T,{show:n.show,onClose:d,maxWidth:"md"},{default:h(()=>[e("div",Ee,[ze,e("div",Pe,[a(Ue,{modelValue:v.value,"onUpdate:modelValue":L[0]||(L[0]=C=>v.value=C),options:_.value},null,8,["modelValue","options"])]),e("div",Te,[e("div",Re,[e("div",Fe,[a(P,{onClick:k,class:"w-full text-xs"},{default:h(()=>[w(" Cancel ")]),_:1})]),e("div",Je,[a(se,{onClick:c,class:"w-full text-xs font-medium"},{default:h(()=>[w(" Apply ")]),_:1})])])])])]),_:1},8,["show"]))}},Xe=["value"],Ye={__name:"MultipleCheckbox",props:{checked:{type:Array,default:()=>[]},value:{default:null}},emits:["update:checked"],setup(n,{emit:i}){const r=n,v=S({get(){return Array.isArray(r.checked)?r.checked.includes(r.value):!1},set(_){const d=Array.isArray(r.checked)?[...r.checked]:[];if(_)d.includes(r.value)||d.push(r.value);else{const c=d.indexOf(r.value);c>-1&&d.splice(c,1)}i("update:checked",d)}});return(_,d)=>R((u(),p("input",{type:"checkbox",value:n.value,"onUpdate:modelValue":d[0]||(d[0]=c=>v.value=c),class:"cursor-pointer rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"},null,8,Xe)),[[te,v.value]])}};const m=n=>(Le("data-v-21307b70"),n=n(),Ae(),n),He={class:"animate-top"},Qe={class:"flex justify-between items-center"},We=m(()=>e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Leads")],-1)),Ze={class:"flex justify-end"},Ke={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},De={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},et=m(()=>e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),tt={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},st={class:"flex justify-end"},lt={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},at={class:"flex justify-end"},ot={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},nt={class:"flex justify-between items-center mb-2"},dt={class:"flex items-center"},it=m(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),ct={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},rt={class:"sm:col-span-3"},ut={class:"relative mt-2"},mt={class:"sm:col-span-3"},vt={class:"relative mt-2"},ht={class:"sm:col-span-3"},_t={class:"relative mt-2"},pt={class:"sm:col-span-3"},ft={class:"relative mt-2"},gt={class:"mt-8 overflow-x-auto sm:rounded-lg"},xt={class:"shadow sm:rounded-lg"},yt={class:"w-full text-sm text-left rtl:text-right text-gray-500"},wt={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},bt={class:"border-b-2"},kt={scope:"col",class:"px-4"},Ct=["onClick"],St=m(()=>e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," NAME ",-1)),Lt=m(()=>e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," COUNTRY ",-1)),At=m(()=>e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," CITY ",-1)),Mt=m(()=>e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," DESIGNATION ",-1)),$t=m(()=>e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," Email ",-1)),qt=m(()=>e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," SEQUENCES ",-1)),Vt=m(()=>e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," ACTION ",-1)),It={key:0},jt={class:""},Nt={class:"pl-3"},Ot={key:0},Bt={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},Ut={class:"px-4 py-2.5 min-w-36"},Et={class:"px-4 py-2.5 min-w-36"},zt={class:"px-4 py-2.5 min-w-36"},Pt={class:"px-4 py-2.5 min-w-36"},Tt={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},Rt={class:"items-center px-4 py-2.5"},Ft={class:"flex items-center justify-start gap-4"},Jt=m(()=>e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)),Gt=m(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),Xt=m(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),Yt=["onClick"],Ht=m(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Qt=m(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)),Wt=[Ht,Qt],Zt={key:1},Kt=m(()=>e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),Dt=[Kt],es={class:"p-6"},ts=m(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1)),ss={class:"mt-6 flex justify-end"},ls={class:"p-6"},as={class:"text-lg font-medium text-gray-900 mb-4"},os={class:"grid grid-cols-1 md:grid-cols-3 gap-4 max-h-96 overflow-y-auto"},ns=["onClick"],ds={class:"mt-6 flex justify-end"},is={class:"w-36"},cs={key:0,class:"fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t border-gray-200 p-4 z-50"},rs={class:"max-w-7xl mx-auto relative flex items-center justify-between"},us={class:"flex items-center space-x-4"},ms={class:"text-gray-700"},vs=m(()=>e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)),hs=[vs],_s={class:"absolute left-1/2 transform -translate-x-1/2"},ps={__name:"List",props:{data:Object,countries:Array,cities:Array,sequences:Array,existingInSequence:Array,allLeadIds:Array,filters:Object},setup(n){var H,Q,W,Z,K;const i=n,r=f(((H=i.filters)==null?void 0:H.search)||""),v=f(((Q=i.filters)==null?void 0:Q.country)||""),_=f(((W=i.filters)==null?void 0:W.city)||""),d=f(((Z=i.filters)==null?void 0:Z.sequence)||""),c=f(((K=i.filters)==null?void 0:K.assignment_status)||"unassigned"),k=ye({}),j=f(!1),L=f(null),C=f(!1),B=f(!1),y=f(null),b=f(!1),o=f([]);we(()=>{const s=localStorage.getItem("selectedLeads");if(s){const t=JSON.parse(s).filter(g=>i.allLeadIds.includes(g));o.value=t,localStorage.setItem("selectedLeads",JSON.stringify(t))}}),E(()=>i.allLeadIds,()=>{o.value=o.value.filter(s=>i.allLeadIds.includes(s)),Y()});const le=S(()=>o.value.length>0),A=s=>i.existingInSequence.includes(s),M=S(()=>i.allLeadIds.filter(s=>!A(s))),F=()=>{B.value=!0},ae=s=>{if(s==="current"){const l=i.data.data.filter(t=>!A(t.id)).map(t=>t.id);o.value=[...new Set([...o.value,...l])],b.value=!0}else s==="all"&&(o.value=[...new Set([...o.value,...M.value])],b.value=!0)},oe=()=>{o.value=[],localStorage.removeItem("selectedLeads"),b.value=!1},J=f(null);E(o,()=>{const s=J.value;s&&(s.indeterminate=o.value.length>0&&o.value.length<M.value.length,M.value.length>0&&M.value.every(l=>o.value.includes(l))?b.value=!0:o.value.length===0&&(b.value=!1))});const ne=S(()=>i.data.data.filter(s=>!A(s.id)).length),de=S(()=>i.data.data.length>0&&i.data.data.every(s=>A(s.id))),ie=()=>{y.value&&Se.post(route("lead-sequence"),{sequence_id:y.value,lead_ids:o.value},{preserveState:!0,preserveScroll:!0,onSuccess:()=>{o.value=[],y.value=null,C.value=!1,F.value=!1,G()}})},G=()=>{o.value=[],localStorage.removeItem("selectedLeads"),b.value=!1},ce=()=>{C.value=!0},X=()=>{C.value=!1,y.value=null},re=s=>{L.value=s,j.value=!0},U=()=>{j.value=!1},ue=()=>{k.delete(route("leads.destroy",L.value),{onSuccess:()=>U()})},$=(s,l,t,g,ge)=>{r.value=s,v.value=l,_.value=t,d.value=g,c.value=ge||"unassigned",k.get(route("leads.index",{search:s,country:l,city:t,sequence:g,assignment_status:c.value}),{preserveState:!0,replace:!0})},me=s=>{v.value=s,$(r.value,s,_.value,d.value,c.value)},ve=s=>{_.value=s,$(r.value,v.value,s,d.value,c.value)},he=s=>{d.value=s,o.value=[],$(r.value,v.value,_.value,s,c.value)},_e=s=>{c.value=s,o.value=[],$(r.value,v.value,_.value,d.value,s)},pe=s=>{console.log(s)},Y=()=>{localStorage.setItem("selectedLeads",JSON.stringify(o.value))};E(o,Y,{deep:!0});const fe=s=>[...new Map(s.map(l=>[l.sequence.id,l.sequence])).values()];return(s,l)=>(u(),p(I,null,[a(be(ke),{title:"Leads"}),a(Me,null,{default:h(()=>[e("div",He,[e("div",Qe,[We,e("div",Ze,[e("div",Ke,[e("div",De,[et,e("input",{id:"search-field",onInput:l[0]||(l[0]=t=>$(t.target.value,v.value,_.value,d.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),e("div",tt,[e("div",st,[a(z,{href:s.route("leads.create")},{default:h(()=>[w(" Add Leads ")]),_:1},8,["href"])])]),e("div",lt,[e("div",at,[a(z,{href:s.route("uploadXls")},{default:h(()=>[w(" Upload XLS ")]),_:1},8,["href"])])])])]),e("div",ot,[e("div",nt,[e("div",dt,[it,a(V,{for:"customer_id",value:"Filters",class:"ml-2"})])]),e("div",ct,[e("div",rt,[a(V,{for:"customer_id",value:"Country"}),e("div",ut,[a(N,{options:[{id:"",name:"All Country"},...n.countries.map(t=>({id:t,name:t}))],modelValue:v.value,"onUpdate:modelValue":l[1]||(l[1]=t=>v.value=t),onOnchange:me},null,8,["options","modelValue"])])]),e("div",mt,[a(V,{for:"customer_id",value:"City"}),e("div",vt,[a(N,{options:[{id:"",name:"All City"},...n.cities.map(t=>({id:t,name:t}))],modelValue:_.value,"onUpdate:modelValue":l[2]||(l[2]=t=>_.value=t),onOnchange:ve},null,8,["options","modelValue"])])]),e("div",ht,[a(V,{for:"customer_id",value:"Sequence"}),e("div",_t,[a(N,{options:[{id:"",name:"All Sequence"},...n.sequences],modelValue:d.value,"onUpdate:modelValue":l[3]||(l[3]=t=>d.value=t),onOnchange:he},null,8,["options","modelValue"])])]),e("div",pt,[a(V,{for:"assignment_status",value:"Assignment Status"}),e("div",ft,[a(N,{options:[{id:"all",name:"All Leads"},{id:"assigned",name:"Assigned Leads"},{id:"unassigned",name:"Unassigned Leads"}],modelValue:c.value,"onUpdate:modelValue":l[4]||(l[4]=t=>c.value=t),onOnchange:_e},null,8,["modelValue"])])])])]),e("div",gt,[e("div",xt,[e("table",yt,[e("thead",wt,[e("tr",bt,[e("th",kt,[de.value?q("",!0):R((u(),p("input",{key:0,type:"checkbox",class:"rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500",ref_key:"selectAllRef",ref:J,"onUpdate:modelValue":l[5]||(l[5]=t=>b.value=t),onClick:Ce(F,["prevent"])},null,8,Ct)),[[te,b.value]])]),St,Lt,At,Mt,$t,qt,Vt])]),n.data.data&&n.data.data.length>0?(u(),p("tbody",It,[(u(!0),p(I,null,O(n.data.data,t=>(u(),p("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",jt,[e("div",Nt,[A(t.id)?q("",!0):(u(),p("div",Ot,[a(Ye,{checked:o.value,"onUpdate:checked":l[6]||(l[6]=g=>o.value=g),value:t.id,onChange:pe},null,8,["checked","value"])]))])]),e("td",Bt,x(t.first_name)+" "+x(t.last_name),1),e("td",Ut,x(t.country),1),e("td",Et,x(t.city),1),e("td",zt,x(t.designation),1),e("td",Pt,x(t.email),1),e("td",Tt,[t.lead_sequences.length>0?(u(!0),p(I,{key:0},O(fe(t.lead_sequences),g=>(u(),p("div",{key:g.id},x(g.name??"-"),1))),128)):q("",!0)]),e("td",Rt,[e("div",Ft,[a($e,{align:"right",width:"48"},{trigger:h(()=>[Jt]),content:h(()=>[a(qe,{href:s.route("leads.edit",{id:t.id})},{svg:h(()=>[Gt]),text:h(()=>[Xt]),_:2},1032,["href"]),e("button",{type:"button",onClick:g=>re(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Wt,8,Yt)]),_:2},1024)])])]))),128))])):(u(),p("tbody",Zt,Dt))])])]),n.data.data&&n.data.data.length>0?(u(),ee(Ie,{key:0,class:"mt-6",links:n.data.links},null,8,["links"])):q("",!0)]),a(T,{show:j.value,onClose:U},{default:h(()=>[e("div",es,[ts,e("div",ss,[a(P,{onClick:U},{default:h(()=>[w(" Cancel ")]),_:1}),a(Ve,{class:"ml-3",onClick:ue},{default:h(()=>[w(" Delete ")]),_:1})])])]),_:1},8,["show"]),a(T,{show:C.value,onClose:X},{default:h(()=>[e("div",ls,[e("h2",as," Select a Sequence for "+x(o.value.length)+" Leads ",1),e("div",os,[(u(!0),p(I,null,O(n.sequences,t=>(u(),p("div",{key:t.id,onClick:g=>y.value=y.value===t.id?null:t.id,class:D(["p-4 border rounded cursor-pointer transition-colors flex justify-between items-center",y.value===t.id?"bg-green-100 border-green-500":"bg-white border-gray-200 hover:bg-gray-50"])},[e("span",{class:D(["truncate",y.value===t.id?"text-green-600":"text-gray-800"])},x(t.name),3)],10,ns))),128))]),e("div",ds,[a(P,{onClick:X},{default:h(()=>[w(" Cancel ")]),_:1}),e("div",is,[a(z,{class:"ml-3 w-20",onClick:ie,disabled:!y.value},{default:h(()=>[w(" Create ")]),_:1},8,["disabled"])])])])]),_:1},8,["show"]),a(Ge,{show:B.value,onClose:l[7]||(l[7]=t=>B.value=!1),onApply:ae,onCancel:oe,"current-page-count":ne.value,"total-count":M.value.length},null,8,["show","current-page-count","total-count"]),le.value?(u(),p("div",cs,[e("div",rs,[e("div",us,[e("span",ms,x(o.value.length)+" selected ",1),e("button",{onClick:G,class:"text-gray-400 hover:text-red-600"},hs)]),e("div",_s,[a(se,{onClick:ce},{default:h(()=>[w(" Select Sequence ")]),_:1})])])])):q("",!0)]),_:1})],64))}},$s=je(ps,[["__scopeId","data-v-21307b70"]]);export{$s as default};

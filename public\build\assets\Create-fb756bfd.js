import{T as y,o as m,c,a,u as o,w as r,F as u,Z as v,b as s,h,k as p,y as x,d as w,g as j,G as S,t as k}from"./app-5886ce47.js";import{_ as V,b as P}from"./AdminLayout-2a8d334f.js";import{_ as n}from"./InputLabel-2616fd75.js";import{_ as d}from"./TextInput-4566519e.js";import{_ as l}from"./InputError-ff051a07.js";import{P as C}from"./PrimaryButton-2c7b1831.js";import"./_plugin-vue_export-helper-c27b6911.js";const D={class:"items-start"},L=s("div",{class:"flex justify-between items-center mb-6"},[s("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New Portfolio Project")],-1),N={class:"bg-white rounded-lg shadow p-6"},U=["onSubmit"],B={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},R={class:"mt-2 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-60 overflow-y-auto border border-gray-300 rounded-md p-4"},T=["id","value"],E=["for"],M=s("p",{class:"mt-1 text-sm text-gray-500"},"Select all technologies used in this project",-1),$={class:"flex mt-6 items-center justify-between"},A={class:"ml-auto flex items-center justify-end gap-x-6"},G=s("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Back",-1),K={__name:"Create",setup(q){const e=y({project_name:"",url:"",description:"",technology:[],login_id:"",password:""}),g=[{id:"html",name:"HTML"},{id:"css",name:"CSS"},{id:"javascript",name:"JavaScript"},{id:"typescript",name:"TypeScript"},{id:"react",name:"React"},{id:"vue",name:"Vue.js"},{id:"angular",name:"Angular"},{id:"svelte",name:"Svelte"},{id:"jquery",name:"jQuery"},{id:"bootstrap",name:"Bootstrap"},{id:"tailwind",name:"Tailwind CSS"},{id:"sass",name:"Sass/SCSS"},{id:"php",name:"PHP"},{id:"laravel",name:"Laravel"},{id:"codeigniter",name:"CodeIgniter"},{id:"symfony",name:"Symfony"},{id:"nodejs",name:"Node.js"},{id:"express",name:"Express.js"},{id:"python",name:"Python"},{id:"django",name:"Django"},{id:"flask",name:"Flask"},{id:"java",name:"Java"},{id:"spring",name:"Spring Boot"},{id:"csharp",name:"C#"},{id:"dotnet",name:".NET"},{id:"ruby",name:"Ruby"},{id:"rails",name:"Ruby on Rails"},{id:"go",name:"Go"},{id:"rust",name:"Rust"},{id:"mysql",name:"MySQL"},{id:"postgresql",name:"PostgreSQL"},{id:"mongodb",name:"MongoDB"},{id:"sqlite",name:"SQLite"},{id:"redis",name:"Redis"},{id:"elasticsearch",name:"Elasticsearch"},{id:"aws",name:"AWS"},{id:"azure",name:"Microsoft Azure"},{id:"gcp",name:"Google Cloud Platform"},{id:"docker",name:"Docker"},{id:"kubernetes",name:"Kubernetes"},{id:"jenkins",name:"Jenkins"},{id:"gitlab",name:"GitLab CI/CD"},{id:"github-actions",name:"GitHub Actions"},{id:"react-native",name:"React Native"},{id:"flutter",name:"Flutter"},{id:"ionic",name:"Ionic"},{id:"xamarin",name:"Xamarin"},{id:"git",name:"Git"},{id:"webpack",name:"Webpack"},{id:"vite",name:"Vite"},{id:"nginx",name:"Nginx"},{id:"apache",name:"Apache"},{id:"linux",name:"Linux"},{id:"ubuntu",name:"Ubuntu"},{id:"centos",name:"CentOS"}],f=()=>{e.post(route("portfolios.store"),{onSuccess:()=>e.reset()})};return(_,i)=>(m(),c(u,null,[a(o(v),{title:"Portfolio"}),a(V,null,{default:r(()=>[s("div",D,[L,s("div",N,[s("form",{onSubmit:h(f,["prevent"]),class:"space-y-6"},[s("div",B,[s("div",null,[a(n,{for:"project_name",value:"Project Name *"}),a(d,{id:"project_name",modelValue:o(e).project_name,"onUpdate:modelValue":i[0]||(i[0]=t=>o(e).project_name=t),type:"text",class:"mt-1 block w-full",required:"",autofocus:"",placeholder:"Enter project name"},null,8,["modelValue"]),a(l,{class:"mt-2",message:o(e).errors.project_name},null,8,["message"])]),s("div",null,[a(n,{for:"url",value:"Project URL"}),a(d,{id:"url",modelValue:o(e).url,"onUpdate:modelValue":i[1]||(i[1]=t=>o(e).url=t),type:"url",class:"mt-1 block w-full",placeholder:"https://example.com"},null,8,["modelValue"]),a(l,{class:"mt-2",message:o(e).errors.url},null,8,["message"])]),s("div",null,[a(n,{for:"description",value:"Description"}),p(s("textarea",{id:"description","onUpdate:modelValue":i[2]||(i[2]=t=>o(e).description=t),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Describe your project..."},null,512),[[x,o(e).description]]),a(l,{class:"mt-2",message:o(e).errors.description},null,8,["message"])]),s("div",null,[a(n,{for:"technology",value:"Technologies Used"}),s("div",R,[(m(),c(u,null,w(g,t=>s("div",{key:t.id,class:"flex items-center"},[p(s("input",{id:`tech-${t.id}`,"onUpdate:modelValue":i[3]||(i[3]=b=>o(e).technology=b),value:t.id,type:"checkbox",class:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"},null,8,T),[[S,o(e).technology]]),s("label",{for:`tech-${t.id}`,class:"ml-2 text-sm text-gray-700"},k(t.name),9,E)])),64))]),M,a(l,{class:"mt-2",message:o(e).errors.technology},null,8,["message"])]),s("div",null,[a(n,{for:"login_id",value:"Login ID"}),a(d,{id:"login_id",modelValue:o(e).login_id,"onUpdate:modelValue":i[4]||(i[4]=t=>o(e).login_id=t),type:"text",class:"mt-1 block w-full",placeholder:"Enter login ID (optional)"},null,8,["modelValue"]),a(l,{class:"mt-2",message:o(e).errors.login_id},null,8,["message"])]),s("div",null,[a(n,{for:"password",value:"Password"}),a(d,{id:"password",modelValue:o(e).password,"onUpdate:modelValue":i[5]||(i[5]=t=>o(e).password=t),type:"text",class:"mt-1 block w-full",placeholder:"Enter password (optional)"},null,8,["modelValue"]),a(l,{class:"mt-2",message:o(e).errors.password},null,8,["message"])])]),s("div",$,[s("div",A,[a(P,{href:_.route("portfolios.index")},{svg:r(()=>[G]),_:1},8,["href"]),a(C,{disabled:o(e).processing},{default:r(()=>[j("Save")]),_:1},8,["disabled"])])])],40,U)])])]),_:1})],64))}};export{K as default};
